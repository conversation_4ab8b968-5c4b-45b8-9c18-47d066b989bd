@echo off
echo === CS2 External Test ===
echo.
echo Checking CS2 process...
tasklist /FI "IMAGENAME eq cs2.exe" 2>NUL | find /I /N "cs2.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo CS2 is running
) else (
    echo CS2 is NOT running - this is expected for testing
)
echo.

echo Checking offset files...
cd "Release v5.1"
if exist "offsets\offsets.json" (
    echo ✓ offsets.json found
) else (
    echo ✗ offsets.json missing
)

if exist "offsets\client_dll.json" (
    echo ✓ client_dll.json found
) else (
    echo ✗ client_dll.json missing
)

if exist "offsets\buttons.json" (
    echo ✓ buttons.json found
) else (
    echo ✗ buttons.json missing
)

echo.
echo Testing program startup (will show error about CS2 not running)...
echo.
External.exe
echo.
echo Program test completed.
pause
