#pragma once
#include <Windows.h>
#include <WinINet.h>
#include <iostream>
#include <fstream>
#include <ctime>
#include <string>
#include <vector>
#include <iomanip>
#include <sstream>
#include <filesystem>
#include <Json/Json.hpp>
#include <Globals/Globals.hpp>

using json = nlohmann::json;
namespace fileSys = std::filesystem;
using namespace std;

#ifdef _WIN32
std::time_t timegm(std::tm* tm) {
    std::time_t t = _mkgmtime(tm);
    return t;
}
#endif _WIN32

class Updater {
private:
    std::vector<std::string> Files = { "offsets.json", "client_dll.json", "buttons.json" };
    string a2x_dumper_api = "https://api.github.com/repos/a2x/cs2-dumper/commits";

    const std::vector<std::pair<std::string, std::string>> file_paths_github = {
        {"https://github.com/a2x/cs2-dumper/raw/main/output/offsets.json", "offsets.json"},
        {"https://github.com/a2x/cs2-dumper/raw/main/output/client_dll.json", "client_dll.json"},
        {"https://github.com/a2x/cs2-dumper/raw/main/output/buttons.json", "buttons.json"},
    };

    inline bool FileExists(const std::string& name) {
        std::ifstream f(name.c_str());
        return f.good();
    }

    inline bool DownloadFile(const std::string& url, const std::string& fileName) {
        HINTERNET hInternet, hConnect;

        hInternet = InternetOpen(L"Updater", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
        if (!hInternet) {
            std::cout << " [Updater] InternetOpen failed." << std::endl;
            return false;
        }

        hConnect = InternetOpenUrlA(hInternet, url.c_str(), NULL, 0, INTERNET_FLAG_RELOAD, 0);
        if (!hConnect) {
            std::cout << " [Updater] InternetOpenUrlA failed." << std::endl;
            InternetCloseHandle(hInternet);
            return false;
        }

        std::ofstream outFile(fileName, std::ios::binary);
        if (!outFile) {
            std::cout << " [Updater] Failed to create local file: " << fileName << std::endl;
            InternetCloseHandle(hConnect);
            InternetCloseHandle(hInternet);
            return false;
        }

        char buffer[4096];
        DWORD bytesRead;

        while (InternetReadFile(hConnect, buffer, sizeof(buffer), &bytesRead) && bytesRead > 0) {
            outFile.write(buffer, bytesRead);
        }

        outFile.close();
        InternetCloseHandle(hConnect);
        InternetCloseHandle(hInternet);

        return true;
    }


    inline bool GetLastCommitInfo(string api, json& commit) {
        HINTERNET hInternet, hConnect;

        hInternet = InternetOpen(L"Updater", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
        if (!hInternet) {
            return false;
        }

        hConnect = InternetOpenUrlA(hInternet, api.c_str(), NULL, 0, INTERNET_FLAG_RELOAD, 0);
        if (!hConnect) {
            InternetCloseHandle(hInternet);
            return false;
        }

        char buffer[4096];
        DWORD bytesRead;
        std::string commitData;

        while (InternetReadFile(hConnect, buffer, sizeof(buffer), &bytesRead) && bytesRead > 0) {
            commitData.append(buffer, bytesRead);
        }

        json data;
        try {
            data = json::parse(commitData);
        }
        catch (const std::exception& e) {
            std::cout << " [Updater] Failed to parse JSON response from GitHub" << std::endl;
            return false;
        }

        if (data.empty())
            return false;

        if (data.is_array()) {
            json last_commit = data[0];
            json last_commit_author = last_commit["commit"]["author"];
            commit = last_commit_author;
        }

        InternetCloseHandle(hConnect);
        InternetCloseHandle(hInternet);

        return true;
    }

    inline bool ParseJsonFromFile(const std::string& fileName, json& outJson) {
        std::ifstream inFile(fileName, std::ios::binary);
        if (!inFile) {
            std::cerr << " [Updater] Failed to open " << fileName << "." << std::endl;
            return false;
        }

        std::string fileContent((std::istreambuf_iterator<char>(inFile)),
            std::istreambuf_iterator<char>());
        inFile.close();

        try {
            outJson = json::parse(fileContent);
        }
        catch (const json::parse_error& e) {
            std::cerr << " [Updater] JSON parse error in " << fileName
                << " at byte " << e.byte << ": " << e.what() << std::endl;
            return false;
        }

        return true;
    }

public:
    inline bool CheckAndDownload() {
        // Ensure the directory exists
        std::string directory = "offsets";
        if (!std::filesystem::exists(directory)) {
            std::filesystem::create_directory(directory);
        }

        json a2x_dumper_commit;

        // Get the last commit information from GitHub
        if (!GetLastCommitInfo(a2x_dumper_api, a2x_dumper_commit)) {
            std::cout << " [Updater] Error getting last commit information from GitHub" << std::endl;
            return false;
        }

        string A2X_Last_Commit_Date = a2x_dumper_commit["date"];
        std::tm Commit_Date_Buffer_A2X = {};
        std::istringstream ssA2X(A2X_Last_Commit_Date);
        ssA2X >> std::get_time(&Commit_Date_Buffer_A2X, "%Y-%m-%dT%H:%M:%SZ");
        std::time_t commit_time_t_A2X = timegm(&Commit_Date_Buffer_A2X);
        auto CommitTimePoint_A2X = std::chrono::system_clock::from_time_t(commit_time_t_A2X);

        for (const auto& file : file_paths_github) {
            const auto& url = file.first;
            std::string localPath = directory + "/" + file.second; // Store files in the offsets directory

            bool fileExists = FileExists(localPath);
            auto lastModifiedTime = fileExists ? fileSys::last_write_time(localPath) : fileSys::file_time_type{};

            auto lastModifiedClockTime = fileExists
                ? std::chrono::time_point_cast<std::chrono::system_clock::duration>(
                    lastModifiedTime - fileSys::file_time_type::clock::now() + std::chrono::system_clock::now())
                : std::chrono::system_clock::time_point{};

            if (fileExists && lastModifiedClockTime >= CommitTimePoint_A2X) {
                std::cout << " [Updater] " << localPath << " is Up-to-Date." << std::endl;
            }
            else {
                if (Updater::DownloadFile(url, localPath)) {
                    std::cout << " [Updater] Successfully downloaded or updated " << localPath << "." << std::endl;
                }
                else {
                    std::cout << " [Updater] Error: Failed to download " << localPath << ". Try downloading manually from " << url << std::endl;
                }
            }
        }
        return true;
    }

    inline bool UpdateOffsets() {
        std::string directory = "offsets";

        // 尝试多个可能的路径
        std::vector<std::string> possiblePaths = {
            "offsets",
            "../offsets",
            "../../offsets"
        };

        std::string workingDirectory;
        bool foundDirectory = false;

        for (const auto& path : possiblePaths) {
            if (std::filesystem::exists(path)) {
                workingDirectory = path;
                foundDirectory = true;
                std::cout << " [Updater] Found offsets directory at: " << path << std::endl;
                break;
            }
        }

        if (!foundDirectory) {
            std::cerr << " [Updater] ERROR: Could not find offsets directory in any expected location!" << std::endl;
            std::cerr << " [Updater] Current working directory: " << std::filesystem::current_path() << std::endl;
            return false;
        }

        std::cout << " [Updater] Starting offset update process..." << std::endl;

        for (const auto& file : Files) {
            std::string filePath = workingDirectory + "/" + file;
            json Data;

            std::cout << " [Updater] Processing file: " << filePath << std::endl;

            if (!FileExists(filePath)) {
                std::cerr << " [Updater] ERROR: " << filePath << " not found." << std::endl;
                return false;
            }

            if (!ParseJsonFromFile(filePath, Data)) {
                std::cerr << " [Updater] ERROR: Failed to parse " << filePath << std::endl;
                return false;
            }

            try {
                if (file == "offsets.json") {
                    std::cout << " [Updater] Updating offsets from offsets.json..." << std::endl;

                    if (!Data.contains("client.dll")) {
                        std::cerr << " [Updater] ERROR: client.dll section not found in offsets.json" << std::endl;
                        return false;
                    }

                    if (!Data.contains("matchmaking.dll")) {
                        std::cerr << " [Updater] ERROR: matchmaking.dll section not found in offsets.json" << std::endl;
                        return false;
                    }

                    const auto& Client = Data["client.dll"];
                    const auto& Matchmaking = Data["matchmaking.dll"];

                    // 安全地更新每个偏移
                    Offsets::dwEntityList = Client.value("dwEntityList", 0);
                    Offsets::dwLocalPlayerPawn = Client.value("dwLocalPlayerPawn", 0);
                    Offsets::dwLocalPlayerController = Client.value("dwLocalPlayerController", 0);
                    Offsets::dwViewAngles = Client.value("dwViewAngles", 0);
                    Offsets::dwViewMatrix = Client.value("dwViewMatrix", 0);
                    Offsets::dwSensitivity = Client.value("dwSensitivity", 0);
                    Offsets::dwSensitivity_sensitivity = Client.value("dwSensitivity_sensitivity", 0);
                    Offsets::dwGameRules = Client.value("dwGameRules", 0);
                    Offsets::dwPlantedC4 = Client.value("dwPlantedC4", 0);
                    Offsets::dwGlobalVars = Client.value("dwGlobalVars", 0);
                    Offsets::dwWeaponC4 = Client.value("dwWeaponC4", 0);
                    Offsets::dwGameTypes = Matchmaking.value("dwGameTypes", 0);

                    if (Matchmaking.contains("dwGameTypes_mapName")) {
                        Offsets::dwGameTypes_mapName = Matchmaking["dwGameTypes_mapName"];
                    }

                    std::cout << " [Updater] Successfully updated offsets.json offsets" << std::endl;
                }
                else if (file == "client_dll.json") {
                    std::cout << " [Updater] Updating offsets from client_dll.json..." << std::endl;

                    if (!Data.contains("client.dll") || !Data["client.dll"].contains("classes")) {
                        std::cerr << " [Updater] ERROR: Invalid structure in client_dll.json" << std::endl;
                        return false;
                    }

                    auto& classes = Data["client.dll"]["classes"];

                    // 安全地更新每个类的偏移
                    if (classes.contains("C_CSPlayerPawn") && classes["C_CSPlayerPawn"].contains("fields")) {
                        auto& C_CSPlayerPawn = classes["C_CSPlayerPawn"]["fields"];
                        Offsets::m_ArmorValue = C_CSPlayerPawn.value("m_ArmorValue", 0);
                        Offsets::m_iShotsFired = C_CSPlayerPawn.value("m_iShotsFired", 0);
                        Offsets::m_aimPunchAngle = C_CSPlayerPawn.value("m_aimPunchAngle", 0);
                        Offsets::m_bIsScoped = C_CSPlayerPawn.value("m_bIsScoped", 0);
                    }

                    if (classes.contains("C_BaseEntity") && classes["C_BaseEntity"].contains("fields")) {
                        auto& C_BaseEntity = classes["C_BaseEntity"]["fields"];
                        Offsets::m_iTeamNum = C_BaseEntity.value("m_iTeamNum", 0);
                        Offsets::m_iHealth = C_BaseEntity.value("m_iHealth", 0);
                        Offsets::m_pGameSceneNode = C_BaseEntity.value("m_pGameSceneNode", 0);
                        Offsets::m_fFlags = C_BaseEntity.value("m_fFlags", 0);
                        Offsets::m_vecAbsVelocity = C_BaseEntity.value("m_vecAbsVelocity", 0);
                        Offsets::m_hOwnerEntity = C_BaseEntity.value("m_hOwnerEntity", 0);
                    }

                    if (classes.contains("CCSPlayerController") && classes["CCSPlayerController"].contains("fields")) {
                        auto& CCSPlayerController = classes["CCSPlayerController"]["fields"];
                        Offsets::m_hPlayerPawn = CCSPlayerController.value("m_hPlayerPawn", 0);
                        Offsets::m_sSanitizedPlayerName = CCSPlayerController.value("m_sSanitizedPlayerName", 0);
                        Offsets::m_iPing = CCSPlayerController.value("m_iPing", 0);
                    }

                    if (classes.contains("C_CSPlayerPawnBase") && classes["C_CSPlayerPawnBase"].contains("fields")) {
                        auto& C_CSPlayerPawnBase = classes["C_CSPlayerPawnBase"]["fields"];
                        Offsets::m_flFlashBangTime = C_CSPlayerPawnBase.value("m_flFlashBangTime", 0);
                        Offsets::m_iIDEntIndex = C_CSPlayerPawnBase.value("m_iIDEntIndex", 0);
                    }

                    if (classes.contains("C_BasePlayerPawn") && classes["C_BasePlayerPawn"].contains("fields")) {
                        auto& C_BasePlayerPawn = classes["C_BasePlayerPawn"]["fields"];
                        Offsets::m_vOldOrigin = C_BasePlayerPawn.value("m_vOldOrigin", 0);
                    }

                    if (classes.contains("C_BaseModelEntity") && classes["C_BaseModelEntity"].contains("fields")) {
                        auto& C_BaseModelEntity = classes["C_BaseModelEntity"]["fields"];
                        Offsets::m_vecViewOffset = C_BaseModelEntity.value("m_vecViewOffset", 0);
                    }

                    if (classes.contains("CGameSceneNode") && classes["CGameSceneNode"].contains("fields")) {
                        auto& CGameSceneNode = classes["CGameSceneNode"]["fields"];
                        Offsets::m_vecAbsOrigin = CGameSceneNode.value("m_vecAbsOrigin", 0);
                    }

                    std::cout << " [Updater] Successfully updated client_dll.json offsets" << std::endl;
                }
                else if (file == "buttons.json") {
                    std::cout << " [Updater] Updating offsets from buttons.json..." << std::endl;

                    if (!Data.contains("client.dll")) {
                        std::cerr << " [Updater] ERROR: client.dll section not found in buttons.json" << std::endl;
                        return false;
                    }

                    const auto& Client = Data["client.dll"];
                    Offsets::dwForceAttack = Client.value("attack", 0);
                    Offsets::dwForceAttack2 = Client.value("attack2", 0);
                    Offsets::dwForceJump = Client.value("jump", 0);

                    std::cout << " [Updater] Successfully updated buttons.json offsets" << std::endl;
                }
            }
            catch (const std::exception& e) {
                std::cerr << " [Updater] EXCEPTION while processing " << file << ": " << e.what() << std::endl;
                return false;
            }
        }

        std::cout << " [Updater] All offsets updated successfully!" << std::endl;
        return true;
    }

    // 添加偏移验证方法
    inline bool ValidateOffsets() {
        std::cout << " [Updater] Validating critical offsets..." << std::endl;

        // 检查关键偏移是否为0（可能表示更新失败）
        if (Offsets::dwEntityList == 0) {
            std::cerr << " [Updater] WARNING: dwEntityList is 0" << std::endl;
            return false;
        }

        if (Offsets::dwLocalPlayerPawn == 0) {
            std::cerr << " [Updater] WARNING: dwLocalPlayerPawn is 0" << std::endl;
            return false;
        }

        if (Offsets::m_iHealth == 0) {
            std::cerr << " [Updater] WARNING: m_iHealth is 0" << std::endl;
            return false;
        }

        std::cout << " [Updater] Offset validation passed!" << std::endl;
        return true;
    }
};

inline Updater updater;