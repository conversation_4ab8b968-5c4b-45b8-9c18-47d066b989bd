﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{880806b2-cdbf-4085-abb3-2d310df62d84}</ProjectGuid>
    <RootNamespace>Cs2External</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IncludePath>D:\Visual Studio\Cs2 Projects\CS2-External-Overlay-Cheat\Cs2 External\Source;D:\Visual Studio\Cs2 Projects\CS2-External-Overlay-Cheat\Cs2 External\Source\ExternalLibs;D:\Visual Studio\Cs2 Projects\CS2-External-Overlay-Cheat\Cs2 External\Cheats;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>D:\Visual Studio\Cs2 Projects\CS2-External-Overlay-Cheat\Cs2 External\Source;D:\Visual Studio\Cs2 Projects\CS2-External-Overlay-Cheat\Cs2 External\Source\ExternalLibs;D:\Visual Studio\Cs2 Projects\CS2-External-Overlay-Cheat\Cs2 External\Cheats;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>WinINet.lib;d3d11.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>WinINet.lib;d3d11.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="Cheats\main.cpp" />
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui.cpp" />
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_demo.cpp" />
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_draw.cpp" />
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_impl_dx11.cpp" />
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_impl_win32.cpp" />
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_tables.cpp" />
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_widgets.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Cheats\Hacks\Aimbot.hpp" />
    <ClInclude Include="Cheats\Hacks\Antiflash.hpp" />
    <ClInclude Include="Cheats\Hacks\Bhop.hpp" />
    <ClInclude Include="Cheats\Hacks\Esp.hpp" />
    <ClInclude Include="Cheats\Hacks\FovChanger.hpp" />
    <ClInclude Include="Cheats\Hacks\Loop.hpp" />
    <ClInclude Include="Cheats\Hacks\Rcs.hpp" />
    <ClInclude Include="Cheats\Hacks\SensiChanger.hpp" />
    <ClInclude Include="Cheats\Hacks\Triggerbot.hpp" />
    <ClInclude Include="Cheats\Readers\Entity.hpp" />
    <ClInclude Include="Cheats\Readers\Game.hpp" />
    <ClInclude Include="Cheats\Readers\LocalPlayer.hpp" />
    <ClInclude Include="Cheats\Utilities\Bomb.hpp" />
    <ClInclude Include="Cheats\Utilities\Bones.hpp" />
    <ClInclude Include="Cheats\Utilities\Config.hpp" />
    <ClInclude Include="Cheats\Utilities\DrawUtils.hpp" />
    <ClInclude Include="Cheats\Utilities\Gamemodes.hpp" />
    <ClInclude Include="Cheats\Utilities\Threads.hpp" />
    <ClInclude Include="Cheats\Utilities\Updater.hpp" />
    <ClInclude Include="Cheats\Utilities\World.hpp" />
    <ClInclude Include="Source\ExternalLibs\Fonts\Poppins.hpp" />
    <ClInclude Include="Source\Functions\Console.hpp" />
    <ClInclude Include="Source\Globals\Globals.hpp" />
    <ClInclude Include="Source\Globals\Includes.hpp" />
    <ClInclude Include="Source\Inputs\Events.hpp" />
    <ClInclude Include="Source\Interfaces\Elements.hpp" />
    <ClInclude Include="Source\Interfaces\Interfaces.hpp" />
    <ClInclude Include="Source\ExternalLibs\ImGui\imconfig.h" />
    <ClInclude Include="Source\ExternalLibs\ImGui\imgui.h" />
    <ClInclude Include="Source\ExternalLibs\ImGui\imgui_impl_dx11.h" />
    <ClInclude Include="Source\ExternalLibs\ImGui\imgui_impl_win32.h" />
    <ClInclude Include="Source\ExternalLibs\ImGui\imgui_internal.h" />
    <ClInclude Include="Source\ExternalLibs\ImGui\imstb_rectpack.h" />
    <ClInclude Include="Source\ExternalLibs\ImGui\imstb_textedit.h" />
    <ClInclude Include="Source\ExternalLibs\ImGui\imstb_truetype.h" />
    <ClInclude Include="Source\ExternalLibs\Json\Json.hpp" />
    <ClInclude Include="Source\Interfaces\Loader.hpp" />
    <ClInclude Include="Source\Interfaces\LSConfigSystem.hpp" />
    <ClInclude Include="Source\Maths\Vector.hpp" />
    <ClInclude Include="Source\Maths\ViewMatrix.hpp" />
    <ClInclude Include="Source\Maths\WorldToScreen.hpp" />
    <ClInclude Include="Source\Memory\Handle.hpp" />
    <ClInclude Include="Source\Memory\Memory.hpp" />
    <ClInclude Include="Source\Memory\Process.hpp" />
    <ClInclude Include="Source\Overlay\ExeConfig.hpp" />
    <ClInclude Include="Source\Overlay\Message.hpp" />
    <ClInclude Include="Source\Overlay\Overlay.hpp" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Source\ExternalLibs\Fonts\Roboto-Bold.hpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>