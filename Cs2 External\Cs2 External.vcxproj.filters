﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_demo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_draw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_impl_dx11.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_impl_win32.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_tables.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Source\ExternalLibs\ImGui\imgui_widgets.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Cheats\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Source\Memory\Memory.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Globals\Includes.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Memory\Process.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Globals\Globals.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Functions\Console.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Readers\Entity.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Readers\Game.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Readers\LocalPlayer.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Utilities\Bones.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ExternalLibs\Fonts\Poppins.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Interfaces\Elements.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Interfaces\Interfaces.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ExternalLibs\ImGui\imconfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ExternalLibs\ImGui\imgui.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ExternalLibs\ImGui\imgui_impl_dx11.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ExternalLibs\ImGui\imgui_impl_win32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ExternalLibs\ImGui\imgui_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ExternalLibs\ImGui\imstb_rectpack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ExternalLibs\ImGui\imstb_textedit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ExternalLibs\ImGui\imstb_truetype.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\ExternalLibs\Json\Json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Interfaces\Loader.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Maths\Vector.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Maths\ViewMatrix.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Maths\WorldToScreen.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Utilities\Config.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Interfaces\LSConfigSystem.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Utilities\Bomb.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Utilities\Updater.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Utilities\DrawUtils.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Utilities\Gamemodes.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Utilities\World.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Utilities\Threads.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Hacks\Esp.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Hacks\Loop.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Memory\Handle.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Hacks\Aimbot.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Inputs\Events.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Hacks\Antiflash.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Hacks\Rcs.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Hacks\Bhop.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Hacks\FovChanger.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Hacks\Triggerbot.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Overlay\Overlay.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cheats\Hacks\SensiChanger.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Overlay\ExeConfig.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Source\Overlay\Message.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="Source\ExternalLibs\Fonts\Roboto-Bold.hpp" />
  </ItemGroup>
</Project>