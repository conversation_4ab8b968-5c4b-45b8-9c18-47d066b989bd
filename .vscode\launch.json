{"version": "0.2.0", "configurations": [{"name": "Debug CS2 External", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/Cs2 External/x64/Debug/Cs2 External.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "console": "externalTerminal", "preLaunchTask": "Build CS2 External (Debug x64)"}, {"name": "Run CS2 External (Release)", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/Cs2 External/x64/Release/Cs2 External.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "console": "externalTerminal", "preLaunchTask": "Build CS2 External (Release x64)"}]}