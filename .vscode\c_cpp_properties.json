{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/Cs2 External/Source/**", "${workspaceFolder}/Cs2 External/Cheats/**", "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/*/include", "C:/Program Files (x86)/Windows Kits/10/Include/*/ucrt", "C:/Program Files (x86)/Windows Kits/10/Include/*/um", "C:/Program Files (x86)/Windows Kits/10/Include/*/shared"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "_WIN32", "_WIN64"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.40.33807/bin/Hostx64/x64/cl.exe", "cStandard": "c17", "cppStandard": "c++20", "intelliSenseMode": "windows-msvc-x64", "configurationProvider": "ms-vscode.cmake-tools"}], "version": 4}