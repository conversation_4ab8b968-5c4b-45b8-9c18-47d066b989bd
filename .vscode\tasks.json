{"version": "2.0.0", "tasks": [{"label": "Build CS2 External (Release x64)", "type": "shell", "command": "msbuild", "args": ["\"Cs2 External.sln\"", "/p:Configuration=Release", "/p:Platform=x64", "/m", "/v:minimal"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile", "options": {"shell": {"executable": "cmd.exe", "args": ["/c", "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\Build\\vcvars64.bat\" && "]}}}, {"label": "Build CS2 External (Debug x64)", "type": "shell", "command": "msbuild", "args": ["\"Cs2 External.sln\"", "/p:Configuration=Debug", "/p:Platform=x64", "/m", "/v:minimal"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile", "options": {"shell": {"executable": "cmd.exe", "args": ["/c", "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\Build\\vcvars64.bat\" && "]}}}, {"label": "Clean Solution", "type": "shell", "command": "msbuild", "args": ["\"Cs2 External.sln\"", "/t:Clean", "/p:Configuration=Release", "/p:Platform=x64"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "options": {"shell": {"executable": "cmd.exe", "args": ["/c", "\"C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Auxiliary\\Build\\vcvars64.bat\" && "]}}}]}