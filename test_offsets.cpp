#include <iostream>
#include <fstream>
#include <filesystem>
#include <Json/Json.hpp>

using json = nlohmann::json;
using namespace std;

bool FileExists(const std::string& name) {
    std::ifstream f(name.c_str());
    return f.good();
}

bool ParseJsonFromFile(const std::string& fileName, json& outJson) {
    std::ifstream inFile(fileName, std::ios::binary);
    if (!inFile) {
        std::cerr << "Failed to open " << fileName << "." << std::endl;
        return false;
    }

    std::string fileContent((std::istreambuf_iterator<char>(inFile)),
        std::istreambuf_iterator<char>());
    inFile.close();

    try {
        outJson = json::parse(fileContent);
    }
    catch (const json::parse_error& e) {
        std::cerr << "JSON parse error in " << fileName
            << " at byte " << e.byte << ": " << e.what() << std::endl;
        return false;
    }

    return true;
}

int main() {
    cout << "=== CS2 External Offset Test ===" << endl;
    cout << "Current working directory: " << std::filesystem::current_path() << endl;
    
    // 测试偏移文件路径
    std::vector<std::string> possiblePaths = {
        "offsets",
        "../offsets",
        "../../offsets"
    };
    
    std::string workingDirectory;
    bool foundDirectory = false;
    
    for (const auto& path : possiblePaths) {
        if (std::filesystem::exists(path)) {
            workingDirectory = path;
            foundDirectory = true;
            cout << "Found offsets directory at: " << path << endl;
            break;
        }
    }
    
    if (!foundDirectory) {
        cerr << "ERROR: Could not find offsets directory!" << endl;
        return 1;
    }
    
    // 测试文件
    std::vector<std::string> Files = { "offsets.json", "client_dll.json", "buttons.json" };
    
    for (const auto& file : Files) {
        std::string filePath = workingDirectory + "/" + file;
        cout << "\n--- Testing file: " << filePath << " ---" << endl;
        
        if (!FileExists(filePath)) {
            cerr << "ERROR: " << filePath << " not found." << endl;
            continue;
        }
        
        json Data;
        if (!ParseJsonFromFile(filePath, Data)) {
            cerr << "ERROR: Failed to parse " << filePath << endl;
            continue;
        }
        
        cout << "✓ File parsed successfully" << endl;
        
        // 测试关键字段
        if (file == "offsets.json") {
            if (Data.contains("client.dll")) {
                auto& client = Data["client.dll"];
                cout << "  dwEntityList: " << client.value("dwEntityList", 0) << endl;
                cout << "  dwLocalPlayerPawn: " << client.value("dwLocalPlayerPawn", 0) << endl;
            }
            if (Data.contains("matchmaking.dll")) {
                auto& mm = Data["matchmaking.dll"];
                cout << "  dwGameTypes: " << mm.value("dwGameTypes", 0) << endl;
                cout << "  dwGameTypes_mapName: " << mm.value("dwGameTypes_mapName", 0) << endl;
            }
        }
        else if (file == "buttons.json") {
            if (Data.contains("client.dll")) {
                auto& client = Data["client.dll"];
                cout << "  attack: " << client.value("attack", 0) << endl;
                cout << "  jump: " << client.value("jump", 0) << endl;
            }
        }
        else if (file == "client_dll.json") {
            if (Data.contains("client.dll") && Data["client.dll"].contains("classes")) {
                cout << "  Classes structure found" << endl;
                auto& classes = Data["client.dll"]["classes"];
                if (classes.contains("C_BaseEntity") && classes["C_BaseEntity"].contains("fields")) {
                    auto& fields = classes["C_BaseEntity"]["fields"];
                    cout << "  m_iHealth: " << fields.value("m_iHealth", 0) << endl;
                }
            }
        }
    }
    
    cout << "\n=== Test completed ===" << endl;
    return 0;
}
